import { useState } from 'react'
import { ChevronDown, ChevronUp, Shield, Clock, FileText, Phone } from 'lucide-react'

const FAQ = () => {
  const [openItems, setOpenItems] = useState({})

  const toggleItem = (index) => {
    setOpenItems(prev => ({
      ...prev,
      [index]: !prev[index]
    }))
  }

  const faqCategories = [
    {
      title: "General Questions",
      icon: <Shield className="w-6 h-6 text-amber-500" />,
      questions: [
        {
          question: "What is a private detective and what do they do?",
          answer: "A private detective is a professional investigator who conducts investigations for private clients, businesses, and organizations. We gather information, conduct surveillance, verify facts, and provide evidence for various personal and corporate matters while operating within legal boundaries."
        },
        {
          question: "Is it legal to hire a private detective in India?",
          answer: "Yes, it is completely legal to hire private detective services in India. While there is no specific central law regulating private detectives, our services are legal as long as we operate within the boundaries of Indian law and respect privacy rights."
        },
        {
          question: "What types of cases do you handle?",
          answer: "We handle a wide range of cases including pre-matrimonial investigations, post-matrimonial investigations, missing person cases, corporate fraud detection, employee background verification, child custody cases, and various other personal and corporate investigation needs."
        },
        {
          question: "How do I know if I need a private detective?",
          answer: "You might need our services if you suspect infidelity, need background verification, are looking for missing persons, require corporate investigation, need evidence for legal proceedings, or have any situation requiring professional investigation and fact-finding."
        }
      ]
    },
    {
      title: "Process & Timeline",
      icon: <Clock className="w-6 h-6 text-amber-500" />,
      questions: [
        {
          question: "How does the investigation process work?",
          answer: "Our process begins with a confidential consultation to understand your needs. We then develop a customized investigation plan, execute the investigation using professional techniques, and provide you with a comprehensive report including evidence and findings."
        },
        {
          question: "How long does an investigation typically take?",
          answer: "Investigation timelines vary depending on the complexity of the case. Simple background checks may take 3-7 days, while complex investigations like matrimonial cases or corporate fraud can take several weeks to months. We provide estimated timelines during consultation."
        },
        {
          question: "What happens during the initial consultation?",
          answer: "During the initial consultation, we discuss your concerns confidentially, assess the feasibility of the investigation, explain our process, provide cost estimates, and answer all your questions. This consultation is typically free and helps us understand your specific needs."
        },
        {
          question: "Can I get updates during the investigation?",
          answer: "Yes, we provide regular updates on the progress of your case while maintaining operational security. The frequency of updates depends on the nature of the investigation and is agreed upon during the initial consultation."
        }
      ]
    },
    {
      title: "Confidentiality & Legal",
      icon: <FileText className="w-6 h-6 text-amber-500" />,
      questions: [
        {
          question: "How do you ensure confidentiality?",
          answer: "We maintain strict confidentiality through secure communication channels, limited access to case information, confidentiality agreements with all staff, secure storage of documents, and never disclosing client information to unauthorized parties."
        },
        {
          question: "Is the evidence you collect admissible in court?",
          answer: "Yes, we follow proper legal procedures to ensure all evidence collected is admissible in court. Our investigators are trained in evidence collection protocols, and we can provide expert testimony when required for legal proceedings."
        },
        {
          question: "What are the legal limitations of private investigation?",
          answer: "We operate within legal boundaries and cannot trespass on private property, hack into systems, impersonate law enforcement, or engage in any illegal activities. We respect privacy laws and ensure all investigations are conducted ethically and legally."
        },
        {
          question: "Do you work with law enforcement?",
          answer: "We cooperate with law enforcement when legally required but maintain client confidentiality. Our investigators include former law enforcement professionals who understand legal procedures and requirements."
        }
      ]
    },
    {
      title: "Costs & Payment",
      icon: <Phone className="w-6 h-6 text-amber-500" />,
      questions: [
        {
          question: "How much do your services cost?",
          answer: "Costs vary depending on the type and complexity of the investigation. We provide detailed cost estimates after understanding your specific requirements during the initial consultation. We offer competitive rates and flexible payment options."
        },
        {
          question: "Do you require advance payment?",
          answer: "Yes, we typically require an advance payment before beginning the investigation. The amount varies based on the estimated scope of work. We also offer milestone-based payment options for longer investigations."
        },
        {
          question: "What payment methods do you accept?",
          answer: "We accept various payment methods including bank transfers, cash payments, and digital payment options. All transactions are documented and receipts are provided for your records."
        },
        {
          question: "Are there any hidden costs?",
          answer: "No, we believe in transparent pricing. All potential costs including investigation fees, travel expenses, and equipment costs are discussed upfront. Any additional expenses are approved by you before being incurred."
        }
      ]
    }
  ]

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="py-20 theme-bg-accent-primary text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Frequently Asked Questions
            </h1>
            <p className="text-xl text-white opacity-90 max-w-3xl mx-auto">
              Find answers to common questions about our private investigation services
            </p>
          </div>
        </div>
      </section>

      {/* FAQ Content */}
      <section className="py-20 theme-bg-primary">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {faqCategories.map((category, categoryIndex) => (
            <div key={categoryIndex} className="mb-12">
              <div className="flex items-center mb-8">
                {category.icon}
                <h2 className="text-2xl font-bold theme-text-primary ml-3">
                  {category.title}
                </h2>
              </div>

              <div className="space-y-4">
                {category.questions.map((faq, faqIndex) => {
                  const itemKey = `${categoryIndex}-${faqIndex}`
                  const isOpen = openItems[itemKey]

                  return (
                    <div key={faqIndex} className="modern-card">
                      <button
                        onClick={() => toggleItem(itemKey)}
                        className="w-full px-6 py-4 text-left flex justify-between items-center hover:theme-bg-secondary transition-colors"
                      >
                        <span className="text-lg font-semibold theme-text-primary">
                          {faq.question}
                        </span>
                        {isOpen ? (
                          <ChevronUp className="w-5 h-5 theme-accent flex-shrink-0" />
                        ) : (
                          <ChevronDown className="w-5 h-5 theme-accent flex-shrink-0" />
                        )}
                      </button>

                      {isOpen && (
                        <div className="px-6 pb-4">
                          <p className="theme-text-secondary leading-relaxed">
                            {faq.answer}
                          </p>
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Additional Help Section */}
      <section className="py-20 theme-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold theme-text-primary mb-4">
              Still Have Questions?
            </h2>
            <p className="text-xl theme-text-secondary max-w-3xl mx-auto">
              Our team is here to help you with any additional questions about our investigation services
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="modern-card p-8 text-center">
              <Phone className="w-12 h-12 theme-accent mx-auto mb-4" />
              <h3 className="text-xl font-semibold theme-text-primary mb-3">Call Us</h3>
              <p className="theme-text-secondary mb-4">
                Speak directly with our investigation experts
              </p>
              <p className="theme-accent font-semibold">+91 90370 80050</p>
            </div>

            <div className="modern-card p-8 text-center">
              <FileText className="w-12 h-12 theme-accent mx-auto mb-4" />
              <h3 className="text-xl font-semibold theme-text-primary mb-3">Email Us</h3>
              <p className="theme-text-secondary mb-4">
                Send us your questions via secure email
              </p>
              <p className="theme-accent font-semibold"><EMAIL></p>
            </div>

            <div className="modern-card p-8 text-center">
              <Shield className="w-12 h-12 theme-accent mx-auto mb-4" />
              <h3 className="text-xl font-semibold theme-text-primary mb-3">Free Consultation</h3>
              <p className="theme-text-secondary mb-4">
                Schedule a confidential consultation
              </p>
              <p className="theme-accent font-semibold">Available 24/7</p>
            </div>
          </div>
        </div>
      </section>

      {/* Legal Disclaimer */}
      <section className="py-12 theme-bg-accent-primary text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-4">Important Legal Notice</h3>
            <p className="text-white opacity-80 max-w-4xl mx-auto text-sm leading-relaxed">
              The information provided on this website is for general informational purposes only and does not constitute legal advice.
              Private investigation services are conducted within the legal framework of Indian law. All investigations are performed
              ethically and with respect for privacy rights. Results may vary based on individual circumstances, and we cannot guarantee
              specific outcomes. For specific legal advice, please consult with a qualified attorney.
            </p>
          </div>
        </div>
      </section>
    </div>
  )
}

export default FAQ

