import { Building, Shield, Search, FileText, Users, Eye, CheckCircle, TrendingUp } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Link } from 'react-router-dom'

const CorporateServices = () => {
  const services = [
    {
      icon: <Users className="w-12 h-12 text-amber-500" />,
      title: "Employee Background Verification",
      description: "Comprehensive screening of potential employees to ensure hiring the right candidates.",
      features: [
        "Educational qualification verification",
        "Employment history validation",
        "Criminal background checks",
        "Reference verification",
        "Address and identity confirmation",
        "Social media screening"
      ]
    },
    {
      icon: <Shield className="w-12 h-12 text-amber-500" />,
      title: "Corporate Fraud Investigation",
      description: "Detection and investigation of financial fraud, embezzlement, and corporate crimes.",
      features: [
        "Financial fraud detection",
        "Embezzlement investigation",
        "Asset misappropriation cases",
        "Accounting irregularities analysis",
        "Internal theft investigation",
        "Forensic accounting support"
      ]
    },
    {
      icon: <Search className="w-12 h-12 text-amber-500" />,
      title: "Due Diligence Investigation",
      description: "Comprehensive business intelligence for mergers, acquisitions, and partnerships.",
      features: [
        "Company background verification",
        "Financial health assessment",
        "Management team evaluation",
        "Market reputation analysis",
        "Legal compliance verification",
        "Risk assessment reporting"
      ]
    },
    {
      icon: <Eye className="w-12 h-12 text-amber-500" />,
      title: "Corporate Surveillance",
      description: "Professional surveillance services for corporate security and investigation needs.",
      features: [
        "Employee misconduct investigation",
        "Workplace harassment cases",
        "Competitor intelligence gathering",
        "Trade secret protection",
        "Executive protection services",
        "Corporate espionage detection"
      ]
    },
    {
      icon: <FileText className="w-12 h-12 text-amber-500" />,
      title: "Litigation Support",
      description: "Professional investigative support for corporate legal proceedings and disputes.",
      features: [
        "Evidence collection and preservation",
        "Witness location and interviews",
        "Document analysis and verification",
        "Expert testimony preparation",
        "Case strategy development",
        "Court-ready reporting"
      ]
    },
    {
      icon: <Building className="w-12 h-12 text-amber-500" />,
      title: "Insurance Investigation",
      description: "Specialized investigation services for insurance claims and fraud detection.",
      features: [
        "Workers' compensation fraud",
        "Property damage assessment",
        "Liability claim investigation",
        "Medical claim verification",
        "Staged accident detection",
        "Insurance fraud prevention"
      ]
    }
  ]

  const industries = [
    "Banking and Financial Services",
    "Information Technology",
    "Manufacturing",
    "Healthcare",
    "Retail and E-commerce",
    "Real Estate",
    "Education",
    "Government Agencies"
  ]

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="py-20 theme-bg-accent-primary text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Corporate Investigation Services
            </h1>
            <p className="text-xl text-white opacity-90 max-w-3xl mx-auto">
              Professional investigation solutions to protect your business interests and ensure corporate security
            </p>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20 theme-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {services.map((service, index) => (
              <div key={index} className="modern-card p-8">
                <div className="flex items-center mb-6">
                  {service.icon}
                  <h3 className="text-2xl font-bold theme-text-primary ml-4">
                    {service.title}
                  </h3>
                </div>
                <p className="theme-text-secondary mb-6">
                  {service.description}
                </p>
                <div className="space-y-3">
                  {service.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center">
                      <CheckCircle className="w-5 h-5 theme-accent mr-3 flex-shrink-0" />
                      <span className="theme-text-secondary">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Industries We Serve */}
      <section className="py-20 theme-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold theme-text-primary mb-4">
              Industries We Serve
            </h2>
            <p className="text-xl theme-text-secondary max-w-3xl mx-auto">
              Our corporate investigation services cater to diverse industries across India
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {industries.map((industry, index) => (
              <div key={index} className="modern-card p-6 text-center">
                <Building className="w-12 h-12 theme-accent mx-auto mb-4" />
                <h3 className="text-lg font-semibold theme-text-primary">
                  {industry}
                </h3>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 theme-bg-accent-primary text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Benefits of Corporate Investigation
            </h2>
            <p className="text-xl text-white opacity-90 max-w-3xl mx-auto">
              Protect your business with professional investigation services
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="text-center">
              <Shield className="w-16 h-16 text-white opacity-90 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-3">Risk Mitigation</h3>
              <p className="text-white opacity-80">
                Identify and mitigate potential risks before they impact your business operations.
              </p>
            </div>
            <div className="text-center">
              <TrendingUp className="w-16 h-16 text-white opacity-90 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-3">Cost Savings</h3>
              <p className="text-white opacity-80">
                Prevent financial losses through early detection of fraud and misconduct.
              </p>
            </div>
            <div className="text-center">
              <FileText className="w-16 h-16 text-white opacity-90 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-3">Legal Protection</h3>
              <p className="text-white opacity-80">
                Gather court-admissible evidence to support legal proceedings and compliance.
              </p>
            </div>
            <div className="text-center">
              <Users className="w-16 h-16 text-white opacity-90 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-3">Employee Trust</h3>
              <p className="text-white opacity-80">
                Build a trustworthy workplace through proper background verification.
              </p>
            </div>
            <div className="text-center">
              <Eye className="w-16 h-16 text-white opacity-90 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-3">Business Intelligence</h3>
              <p className="text-white opacity-80">
                Gain valuable insights about competitors and market conditions.
              </p>
            </div>
            <div className="text-center">
              <Building className="w-16 h-16 text-white opacity-90 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-3">Reputation Protection</h3>
              <p className="text-white opacity-80">
                Protect your company's reputation through proactive investigation services.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 theme-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold theme-text-primary mb-4">
              Our Corporate Investigation Process
            </h2>
            <p className="text-xl theme-text-secondary max-w-3xl mx-auto">
              A systematic approach to corporate investigations ensuring thorough and reliable results
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-20 h-20 bg-white text-black rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold">1</span>
              </div>
              <h3 className="text-xl font-semibold theme-text-primary mb-3">Initial Assessment</h3>
              <p className="theme-text-secondary">
                Understand your business needs and investigation requirements
              </p>
            </div>
            <div className="text-center">
              <div className="w-20 h-20 bg-white text-black rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold">2</span>
              </div>
              <h3 className="text-xl font-semibold theme-text-primary mb-3">Strategy Development</h3>
              <p className="theme-text-secondary">
                Create a customized investigation plan with clear objectives
              </p>
            </div>
            <div className="text-center">
              <div className="w-20 h-20 bg-white text-black rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold">3</span>
              </div>
              <h3 className="text-xl font-semibold theme-text-primary mb-3">Investigation Execution</h3>
              <p className="theme-text-secondary">
                Conduct thorough investigation using professional methods
              </p>
            </div>
            <div className="text-center">
              <div className="w-20 h-20 bg-white text-black rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold">4</span>
              </div>
              <h3 className="text-xl font-semibold theme-text-primary mb-3">Detailed Reporting</h3>
              <p className="theme-text-secondary">
                Provide comprehensive reports with actionable recommendations
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-amber-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Protect Your Business Today
          </h2>
          <p className="text-xl text-amber-100 mb-8 max-w-3xl mx-auto">
            Contact us to discuss your corporate investigation needs and learn how we can help protect your business interests.
          </p>
          <Button size="lg" className="modern-btn-outline">
            <Link to="/contact">Schedule Business Consultation</Link>
          </Button>
        </div>
      </section>
    </div>
  )
}

export default CorporateServices

