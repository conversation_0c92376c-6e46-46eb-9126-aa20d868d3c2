import { useState, useEffect } from 'react'
import { Calendar, Clock, Shield, CheckCircle, AlertCircle, X, User, Phone, Mail } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { submitToGoogleForm } from '@/config/googleForm'

const Consultation = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    serviceType: '',
    urgency: '',
    preferredTime: '',
    caseDetails: '',
    confidentialityLevel: ''
  })
  
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState(null)
  const [showNotification, setShowNotification] = useState(false)

  // Scroll to top on component mount
  useEffect(() => {
    window.scrollTo(0, 0)
  }, [])

  // Auto-dismiss success notification after 8 seconds
  useEffect(() => {
    if (submitStatus?.type === 'success') {
      setShowNotification(true)
      const timer = setTimeout(() => {
        setShowNotification(false)
        setTimeout(() => setSubmitStatus(null), 300)
      }, 8000)
      return () => clearTimeout(timer)
    } else if (submitStatus?.type === 'error') {
      setShowNotification(true)
    }
  }, [submitStatus])

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)
    setSubmitStatus(null)

    try {
      // Create a formatted message for the consultation request
      const consultationMessage = `
CONSULTATION REQUEST:
Service Type: ${formData.serviceType}
Urgency Level: ${formData.urgency}
Preferred Time: ${formData.preferredTime}
Confidentiality Level: ${formData.confidentialityLevel}

Case Details:
${formData.caseDetails}
      `.trim()

      const submissionData = {
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        serviceType: `Consultation - ${formData.serviceType}`,
        message: consultationMessage
      }

      const result = await submitToGoogleForm(submissionData)
      
      if (result.success) {
        setSubmitStatus({ 
          type: 'success', 
          message: 'Consultation request submitted successfully! Our team will contact you within 2 hours to schedule your confidential consultation.' 
        })
        setFormData({
          name: '',
          email: '',
          phone: '',
          serviceType: '',
          urgency: '',
          preferredTime: '',
          caseDetails: '',
          confidentialityLevel: ''
        })
      } else {
        setSubmitStatus({ 
          type: 'error', 
          message: 'There was an error submitting your consultation request. Please try again or call us directly at +91 90374 83600.' 
        })
      }
    } catch (error) {
      setSubmitStatus({ 
        type: 'error', 
        message: 'Network error. Please check your connection and try again.' 
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const consultationFeatures = [
    {
      icon: <Shield className="w-6 h-6 text-amber-500" />,
      title: "100% Confidential",
      description: "All consultations are completely confidential and protected by professional privacy standards."
    },
    {
      icon: <Clock className="w-6 h-6 text-amber-500" />,
      title: "Quick Response",
      description: "We respond to consultation requests within 2 hours during business hours."
    },
    {
      icon: <User className="w-6 h-6 text-amber-500" />,
      title: "Expert Guidance",
      description: "Speak directly with experienced investigators who understand your situation."
    },
    {
      icon: <Calendar className="w-6 h-6 text-amber-500" />,
      title: "Flexible Scheduling",
      description: "We accommodate your schedule with evening and weekend consultation options."
    }
  ]

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="py-20 theme-bg-accent-primary text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Free Confidential Consultation
            </h1>
            <p className="text-xl text-white opacity-90 max-w-3xl mx-auto">
              Get expert guidance on your investigation needs. Our experienced team will help you understand your options and develop the right approach for your situation.
            </p>
          </div>
        </div>
      </section>

      {/* Consultation Features */}
      <section className="py-16 theme-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {consultationFeatures.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="flex justify-center mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-lg font-semibold theme-text-primary mb-2">
                  {feature.title}
                </h3>
                <p className="theme-text-secondary text-sm">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Consultation Form */}
      <section className="py-20 theme-bg-secondary">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="modern-card p-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold theme-text-primary mb-4">
                Request Your Consultation
              </h2>
              <p className="theme-text-secondary">
                Fill out the form below and we'll contact you within 2 hours to schedule your confidential consultation.
              </p>
            </div>

            {/* Success/Error Notification */}
            {submitStatus && showNotification && (
              <div className={`mb-6 p-4 rounded-lg border transition-all duration-300 ${
                submitStatus.type === 'success'
                  ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-700'
                  : 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-700'
              } ${showNotification ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-2'}`}>
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    {submitStatus.type === 'success' ? (
                      <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                    ) : (
                      <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
                    )}
                  </div>
                  <div className="ml-3 flex-1">
                    <p className={`text-sm font-medium ${
                      submitStatus.type === 'success'
                        ? 'text-green-800 dark:text-green-200'
                        : 'text-red-800 dark:text-red-200'
                    }`}>
                      {submitStatus.message}
                    </p>
                    {submitStatus.type === 'success' && (
                      <div className="mt-2">
                        <div className="w-full bg-green-200 dark:bg-green-700 rounded-full h-1.5">
                          <div className="bg-green-600 dark:bg-green-400 h-1.5 rounded-full animate-pulse" style={{width: '100%'}}></div>
                        </div>
                        <p className="text-xs text-green-600 dark:text-green-400 mt-1 animate-pulse">We will contact you soon...</p>
                      </div>
                    )}
                  </div>
                  <button
                    onClick={() => {
                      setShowNotification(false)
                      setTimeout(() => setSubmitStatus(null), 300)
                    }}
                    className="ml-2 theme-text-light hover:theme-text-primary transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            )}
            
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Personal Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium theme-text-primary mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    required
                    value={formData.name}
                    onChange={handleChange}
                    className="w-full px-4 py-3 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-white transition-all duration-200 shadow-sm"
                    placeholder="Your full name"
                  />
                </div>
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium theme-text-primary mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    required
                    value={formData.phone}
                    onChange={handleChange}
                    className="w-full px-4 py-3 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-white transition-all duration-200 shadow-sm"
                    placeholder="+91 90374 83600"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium theme-text-primary mb-2">
                  Email Address *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full px-4 py-3 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-white transition-all duration-200 shadow-sm"
                  placeholder="<EMAIL>"
                />
              </div>

              {/* Service and Urgency */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="serviceType" className="block text-sm font-medium theme-text-primary mb-2">
                    Service Type *
                  </label>
                  <select
                    id="serviceType"
                    name="serviceType"
                    required
                    value={formData.serviceType}
                    onChange={handleChange}
                    className="w-full px-4 py-3 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-white transition-all duration-200 shadow-sm"
                  >
                    <option value="">Select a service</option>
                    <option value="pre-matrimonial">Pre-Matrimonial Investigation</option>
                    <option value="post-matrimonial">Post-Matrimonial Investigation</option>
                    <option value="missing-person">Missing Person Investigation</option>
                    <option value="corporate">Corporate Investigation</option>
                    <option value="background-check">Background Verification</option>
                    <option value="surveillance">Surveillance Services</option>
                    <option value="fraud-investigation">Fraud Investigation</option>
                    <option value="other">Other</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="urgency" className="block text-sm font-medium theme-text-primary mb-2">
                    Urgency Level *
                  </label>
                  <select
                    id="urgency"
                    name="urgency"
                    required
                    value={formData.urgency}
                    onChange={handleChange}
                    className="w-full px-4 py-3 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-white transition-all duration-200 shadow-sm"
                  >
                    <option value="">Select urgency</option>
                    <option value="emergency">Emergency (24-48 hours)</option>
                    <option value="urgent">Urgent (Within 1 week)</option>
                    <option value="normal">Normal (Within 2 weeks)</option>
                    <option value="flexible">Flexible timing</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="preferredTime" className="block text-sm font-medium theme-text-primary mb-2">
                    Preferred Consultation Time
                  </label>
                  <select
                    id="preferredTime"
                    name="preferredTime"
                    value={formData.preferredTime}
                    onChange={handleChange}
                    className="w-full px-4 py-3 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-white transition-all duration-200 shadow-sm"
                  >
                    <option value="">Select preferred time</option>
                    <option value="morning">Morning (9 AM - 12 PM)</option>
                    <option value="afternoon">Afternoon (12 PM - 5 PM)</option>
                    <option value="evening">Evening (5 PM - 8 PM)</option>
                    <option value="weekend">Weekend</option>
                    <option value="flexible">Flexible</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="confidentialityLevel" className="block text-sm font-medium theme-text-primary mb-2">
                    Confidentiality Level *
                  </label>
                  <select
                    id="confidentialityLevel"
                    name="confidentialityLevel"
                    required
                    value={formData.confidentialityLevel}
                    onChange={handleChange}
                    className="w-full px-4 py-3 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-white transition-all duration-200 shadow-sm"
                  >
                    <option value="">Select confidentiality level</option>
                    <option value="standard">Standard Confidentiality</option>
                    <option value="high">High Confidentiality</option>
                    <option value="maximum">Maximum Confidentiality</option>
                  </select>
                </div>
              </div>

              <div>
                <label htmlFor="caseDetails" className="block text-sm font-medium theme-text-primary mb-2">
                  Case Details *
                </label>
                <textarea
                  id="caseDetails"
                  name="caseDetails"
                  required
                  rows={6}
                  value={formData.caseDetails}
                  onChange={handleChange}
                  className="w-full px-4 py-3 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-white transition-all duration-200 shadow-sm resize-none"
                  placeholder="Please provide details about your situation. Include any relevant background information, specific concerns, and what you hope to achieve. All information is strictly confidential."
                />
              </div>

              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <Shield className="w-5 h-5 text-amber-600 flex-shrink-0 mt-0.5" />
                  <div className="text-sm text-amber-700">
                    <p className="font-medium mb-1">Confidentiality Guarantee</p>
                    <p>All information shared during consultation is protected by professional confidentiality standards. We never share client information without explicit consent.</p>
                  </div>
                </div>
              </div>

              <Button
                type="submit"
                disabled={isSubmitting}
                className="w-full modern-btn text-white py-4 text-lg font-semibold"
              >
                {isSubmitting ? (
                  <div className="flex items-center justify-center">
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Submitting Request...
                  </div>
                ) : (
                  <div className="flex items-center justify-center">
                    <Calendar className="w-5 h-5 mr-2" />
                    Request Free Consultation
                  </div>
                )}
              </Button>
            </form>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Consultation
