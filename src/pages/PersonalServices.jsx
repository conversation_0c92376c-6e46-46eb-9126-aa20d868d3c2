import { Heart, Eye, Users, Shield, Search, FileText, Clock, CheckCircle } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Link } from 'react-router-dom'

const PersonalServices = () => {
  const services = [
    {
      icon: <Heart className="w-12 h-12 text-amber-500" />,
      title: "Pre-Matrimonial Investigation",
      description: "Comprehensive background verification of prospective spouses to ensure a secure future.",
      features: [
        "Character and reputation verification",
        "Family background investigation",
        "Financial status assessment",
        "Educational qualification verification",
        "Employment history check",
        "Social media and online presence analysis"
      ]
    },
    {
      icon: <Eye className="w-12 h-12 text-amber-500" />,
      title: "Post-Matrimonial Investigation",
      description: "Discreet investigation of marital issues including infidelity and suspicious behavior.",
      features: [
        "Spouse surveillance and monitoring",
        "Infidelity investigation with evidence",
        "Asset investigation and hidden finances",
        "Behavioral pattern analysis",
        "Digital forensics and communication tracking",
        "Court-admissible evidence collection"
      ]
    },
    {
      icon: <Search className="w-12 h-12 text-amber-500" />,
      title: "Missing Person Investigation",
      description: "Professional tracing services to locate missing individuals with advanced techniques.",
      features: [
        "Runaway teenagers and family members",
        "Lost relatives and friends",
        "Absconding debtors and defaulters",
        "Witness location for legal cases",
        "Old friend and classmate tracing",
        "International missing person cases"
      ]
    },
    {
      icon: <Users className="w-12 h-12 text-amber-500" />,
      title: "Child Custody Investigation",
      description: "Investigative support for child custody cases and parental fitness evaluation.",
      features: [
        "Parental fitness assessment",
        "Living condition evaluation",
        "Child welfare investigation",
        "Custody violation documentation",
        "Parental behavior monitoring",
        "Expert testimony support"
      ]
    },
    {
      icon: <Shield className="w-12 h-12 text-amber-500" />,
      title: "Personal Security Assessment",
      description: "Comprehensive security evaluation for individuals and their families.",
      features: [
        "Threat assessment and analysis",
        "Personal safety recommendations",
        "Home security evaluation",
        "Stalking and harassment investigation",
        "Background checks on domestic staff",
        "Security protocol development"
      ]
    },
    {
      icon: <FileText className="w-12 h-12 text-amber-500" />,
      title: "Evidence Collection",
      description: "Professional evidence gathering for legal proceedings and personal matters.",
      features: [
        "Court-admissible evidence collection",
        "Photographic and video documentation",
        "Witness statement recording",
        "Document verification and analysis",
        "Digital evidence preservation",
        "Expert testimony preparation"
      ]
    }
  ]

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="py-20 theme-bg-accent-primary text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Personal Investigation Services
            </h1>
            <p className="text-xl text-white opacity-90 max-w-3xl mx-auto">
              Confidential and professional investigation services for personal matters requiring discretion and expertise
            </p>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20 theme-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {services.map((service, index) => (
              <div key={index} className="modern-card p-8">
                <div className="flex items-center mb-6">
                  {service.icon}
                  <h3 className="text-2xl font-bold theme-text-primary ml-4">
                    {service.title}
                  </h3>
                </div>
                <p className="theme-text-secondary mb-6">
                  {service.description}
                </p>
                <div className="space-y-3">
                  {service.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center">
                      <CheckCircle className="w-5 h-5 theme-accent mr-3 flex-shrink-0" />
                      <span className="theme-text-secondary">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 theme-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold theme-text-primary mb-4">
              Our Investigation Process
            </h2>
            <p className="text-xl theme-text-secondary max-w-3xl mx-auto">
              A systematic and confidential approach to personal investigations
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-20 h-20 bg-white text-black rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="w-10 h-10" />
              </div>
              <h3 className="text-xl font-semibold theme-text-primary mb-3">Initial Consultation</h3>
              <p className="theme-text-secondary">
                Confidential discussion to understand your concerns and requirements
              </p>
            </div>
            <div className="text-center">
              <div className="w-20 h-20 bg-white text-black rounded-full flex items-center justify-center mx-auto mb-4">
                <FileText className="w-10 h-10" />
              </div>
              <h3 className="text-xl font-semibold theme-text-primary mb-3">Case Planning</h3>
              <p className="theme-text-secondary">
                Develop a customized investigation strategy and timeline
              </p>
            </div>
            <div className="text-center">
              <div className="w-20 h-20 bg-white text-black rounded-full flex items-center justify-center mx-auto mb-4">
                <Search className="w-10 h-10" />
              </div>
              <h3 className="text-xl font-semibold theme-text-primary mb-3">Investigation</h3>
              <p className="theme-text-secondary">
                Execute the investigation using professional and discreet methods
              </p>
            </div>
            <div className="text-center">
              <div className="w-20 h-20 bg-white text-black rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="w-10 h-10" />
              </div>
              <h3 className="text-xl font-semibold theme-text-primary mb-3">Reporting</h3>
              <p className="theme-text-secondary">
                Deliver comprehensive report with evidence and recommendations
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-20 theme-bg-accent-primary text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Why Choose Our Personal Investigation Services?
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <Shield className="w-16 h-16 text-white opacity-90 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-3">Complete Confidentiality</h3>
              <p className="text-white opacity-80">
                Your privacy is our top priority. All investigations are conducted with utmost discretion.
              </p>
            </div>
            <div className="text-center">
              <Users className="w-16 h-16 text-white opacity-90 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-3">Experienced Team</h3>
              <p className="text-white opacity-80">
                Our investigators have extensive experience in handling sensitive personal matters.
              </p>
            </div>
            <div className="text-center">
              <CheckCircle className="w-16 h-16 text-white opacity-90 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-3">Reliable Results</h3>
              <p className="text-white opacity-80">
                We provide accurate, court-admissible evidence and comprehensive reporting.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-amber-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Need Personal Investigation Services?
          </h2>
          <p className="text-xl text-amber-100 mb-8 max-w-3xl mx-auto">
            Contact us for a confidential consultation. We understand the sensitive nature of personal matters.
          </p>
          <Button size="lg" className="modern-btn-outline">
            <Link to="/contact">Get Confidential Consultation</Link>
          </Button>
        </div>
      </section>
    </div>
  )
}

export default PersonalServices

