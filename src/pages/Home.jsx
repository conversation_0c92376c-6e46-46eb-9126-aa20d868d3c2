import { Link } from 'react-router-dom'
import { Shield, Eye, Lock, Users, CheckCircle, ArrowRight, Search, Clock, Award } from 'lucide-react'
import { useEffect, useState } from 'react'

const Home = () => {
  const [scrollY, setScrollY] = useState(0)
  const [isVisible, setIsVisible] = useState({})

  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY)
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          setIsVisible(prev => ({
            ...prev,
            [entry.target.id]: entry.isIntersecting
          }))
        })
      },
      { threshold: 0.1 }
    )

    document.querySelectorAll('[id]').forEach((el) => {
      observer.observe(el)
    })

    return () => observer.disconnect()
  }, [])

  const features = [
    {
      icon: <Shield className="w-12 h-12 theme-accent" />,
      title: "Uncover Hidden Truths",
      description: "When doubt clouds your judgment, we bring clarity through professional investigation and evidence gathering.",
      problem: "Suspicious behavior keeping you awake?"
    },
    {
      icon: <Lock className="w-12 h-12 theme-accent" />,
      title: "Protect Your Interests",
      description: "Safeguard your personal and business relationships with discreet verification and background checks.",
      problem: "Worried about who you can trust?"
    },
    {
      icon: <Eye className="w-12 h-12 theme-accent" />,
      title: "Find Missing Answers",
      description: "Locate missing persons, uncover fraud, and get the evidence you need for important decisions.",
      problem: "Need proof but don't know where to start?"
    },
    {
      icon: <Users className="w-12 h-12 theme-accent" />,
      title: "Make Informed Decisions",
      description: "Get comprehensive reports that help you make confident choices about relationships and business.",
      problem: "Facing a major life decision?"
    }
  ]

  const solutions = [
    {
      problem: "Relationship Concerns",
      title: "Personal Investigation Solutions",
      description: "When trust is questioned, get the clarity you deserve through discreet verification and evidence gathering.",
      features: ["Pre-matrimonial verification", "Infidelity investigations", "Missing person tracing", "Family dispute resolution"],
      link: "/services/personal",
      icon: <Search className="w-8 h-8 theme-accent" />
    },
    {
      problem: "Business Security",
      title: "Corporate Protection Services",
      description: "Protect your business interests with comprehensive background checks and fraud detection services.",
      features: ["Employee verification", "Fraud investigation", "Business intelligence", "Legal support"],
      link: "/services/corporate",
      icon: <Shield className="w-8 h-8 theme-accent" />
    }
  ]



  return (
    <div className="overflow-hidden">
      {/* Hero Section*/}
      <section className="relative min-h-screen flex items-center theme-bg-primary overflow-hidden">
        {/* Modern geometric background */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-72 h-72 theme-bg-accent-primary opacity-10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-20 w-96 h-96 theme-bg-accent-primary opacity-5 rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] theme-bg-accent-primary opacity-5 rounded-full blur-3xl"></div>
        </div>

        {/* Content + Image */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex flex-col lg:flex-row items-center justify-between w-full">
          
          {/* Text Content (Left) */}
          <div 
            className={`text-center lg:text-left transition-all duration-1000 ${
              scrollY < 100 ? 'opacity-100 translate-y-0' : 'opacity-80 translate-y-4'
            } lg:w-1/2`}
          >
            <div className="mb-6">
              <span className="inline-block px-6 py-3 theme-bg-accent-primary/20 theme-accent rounded-full text-sm font-medium mb-4 border border-current/20">
                When Truth Matters Most
              </span>
            </div>

            <h1 className="text-5xl md:text-7xl font-bold theme-text-primary mb-6 leading-tight">
              <span className="block">Uncover the</span>
              <span className="block modern-heading">
                Truth You Need
              </span>
            </h1>

            <p className="text-xl md:text-2xl theme-text-secondary mb-8 max-w-2xl lg:mx-0 mx-auto leading-relaxed">
              Professional investigation services that bring clarity to life's most challenging situations.
              Discreet, ethical, and results-driven.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center lg:justify-start items-center">
              <Link to="/contact" className="modern-btn text-lg px-8 py-4 text-white font-semibold flex items-center">
                Get Your Answers
                <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </Link>
              <Link to="/services" className="modern-btn-outline text-lg px-8 py-4 font-semibold">
                Explore Solutions
              </Link>
            </div>

            <div className="mt-12 flex justify-center lg:justify-start items-center space-x-8 theme-text-secondary">
              <div className="w-px h-8 theme-border"></div>
              <div className="text-center">
                <div className="text-2xl font-bold theme-accent">24/7</div>
                <div className="text-sm">Available</div>
              </div>
              <div className="w-px h-8 theme-border"></div>
              <div className="text-center">
                <div className="text-2xl font-bold theme-accent">100%</div>
                <div className="text-sm">Confidential</div>
              </div>
            </div>
          </div>
          { /* Image right*/}
          <div className="lg:w-1/2 w-full mt-12 lg:mt-0 flex justify-center">
            <img 
              src="/Untitled design.png" 
              alt="Investigation Services" 
              className="w-full h-auto max-w-sm lg:max-w-md xl:max-w-lg object-contain rounded-3xl transition-shadow duration-300 ease-in-out hover:shadow-lg hover:translate-y-[-4px]"
            />
          </div>
        </div>  

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 theme-border rounded-full flex justify-center">
            <div className="w-1 h-3 theme-bg-accent-primary rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>


      {/* Problem-Solution Section */}
      <section id="solutions" className="py-24 theme-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div 
            className={`text-center mb-20 transition-all duration-1000 ${
              isVisible.solutions ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
            }`}
          >
            <span className="inline-block px-4 py-2 bg-amber-100 text-amber-800 rounded-full text-sm font-medium mb-4">
              When Life Gets Complicated
            </span>
            <h2 className="text-4xl md:text-5xl font-bold theme-text-primary mb-6">
              We Understand Your
              <span className="block modern-heading">Concerns</span>
            </h2>
            <p className="text-xl theme-text-secondary max-w-3xl mx-auto">
              Every situation is unique. We provide personalized solutions to help you navigate life's uncertainties with confidence.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div 
                key={index} 
                className={`group modern-card p-8 transition-all duration-500 ${
                  isVisible.solutions ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
                }`}
                style={{ transitionDelay: `${index * 150}ms` }}
              >
                <div className="mb-6 group-hover:scale-110 transition-transform duration-300">
                  {feature.icon}
                </div>
                <div className="mb-4">
                  <span className="text-sm theme-accent font-medium">{feature.problem}</span>
                </div>
                <h3 className="text-xl font-bold theme-text-primary mb-4 group-hover:theme-accent transition-colors">
                  {feature.title}
                </h3>
                <p className="theme-text-secondary leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Solutions Section */}
      <section id="services" className="py-24 theme-bg-accent">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div 
            className={`text-center mb-20 transition-all duration-1000 ${
              isVisible.services ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
            }`}
          >
            <span className="inline-block px-4 py-2 theme-bg-card theme-text-secondary rounded-full text-sm font-medium mb-4">
              Tailored Solutions
            </span>
            <h2 className="text-4xl md:text-5xl font-bold theme-text-primary mb-6">
              How We Can
              <span className="block modern-heading">Help You</span>
            </h2>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {solutions.map((solution, index) => (
              <div 
                key={index} 
                className={`group relative overflow-hidden modern-card p-10 transition-all duration-700 ${
                  isVisible.services ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
                }`}
                style={{ transitionDelay: `${index * 200}ms` }}
              >
                <div className="absolute top-0 left-0 w-full h-2 theme-gradient"></div>
                
                <div className="flex items-center mb-6">
                  <div className="p-3 bg-amber-100 rounded-xl mr-4 group-hover:bg-amber-200 transition-colors">
                    {solution.icon}
                  </div>
                  <div>
                    <span className="text-sm theme-accent font-medium">{solution.problem}</span>
                    <h3 className="text-2xl font-bold theme-text-primary group-hover:theme-accent transition-colors">
                      {solution.title}
                    </h3>
                  </div>
                </div>
                
                <p className="theme-text-secondary mb-8 text-lg leading-relaxed">
                  {solution.description}
                </p>
                
                <div className="grid grid-cols-2 gap-4 mb-8">
                  {solution.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center">
                      <CheckCircle className="w-5 h-5 theme-accent mr-3 flex-shrink-0" />
                      <span className="theme-text-secondary text-sm">{feature}</span>
                    </div>
                  ))}
                </div>
                
                <button className="w-full modern-btn py-4 px-6 text-lg font-semibold">
                  <Link to={solution.link} className="flex items-center justify-center">
                    Learn More
                    <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Trust & Results Section */}
      <section id="stats" className="py-24 theme-bg-secondary relative overflow-hidden">
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 left-20 w-32 h-32 theme-bg-accent-primary rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-20 w-40 h-40 theme-bg-accent-primary rounded-full blur-3xl"></div>
        </div>
        
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div 
            className={`text-center mb-20 transition-all duration-1000 ${
              isVisible.stats ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
            }`}
          >
            <span className="inline-block px-4 py-2 theme-bg-accent-primary/20 theme-accent rounded-full text-sm font-medium mb-4">
              Proven Track Record
            </span>
            <h2 className="text-4xl md:text-5xl font-bold theme-text-primary mb-6">
              Results That
              <span className="block modern-heading">Speak for Themselves</span>
            </h2>
            <p className="text-xl theme-text-secondary max-w-3xl mx-auto">
              Our commitment to excellence has earned the trust of clients nationwide
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {[
              { number: "100%", label: "Confidential", icon: <Shield className="w-8 h-8 theme-accent" /> },
              { number: "24/7", label: "Support Available", icon: <Clock className="w-8 h-8 theme-accent" /> },
              { number: "Modern", label: "Technology", icon: <Award className="w-8 h-8 theme-accent" /> },
              { number: "Licensed", label: "Professionals", icon: <CheckCircle className="w-8 h-8 theme-accent" /> }
            ].map((stat, index) => (
              <div
                key={index}
                className={`text-center group transition-all duration-1000 modern-card p-6 ${
                  isVisible.stats ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
                }`}
                style={{ transitionDelay: `${index * 150}ms` }}
              >
                <div className="mb-4 flex justify-center group-hover:scale-110 transition-transform duration-300">
                  {stat.icon}
                </div>
                <div className="text-4xl md:text-5xl font-bold theme-accent mb-2 group-hover:scale-105 transition-transform">
                  {stat.number}
                </div>
                <div className="theme-text-secondary font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Our Commitment Section */}
      <section id="commitment" className="py-24 theme-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div
            className={`text-center mb-20 transition-all duration-1000 ${
              isVisible.commitment ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
            }`}
          >
            <span className="inline-block px-4 py-2 bg-amber-100 text-amber-800 rounded-full text-sm font-medium mb-4">
              Our Promise
            </span>
            <h2 className="text-4xl md:text-5xl font-bold theme-text-primary mb-6">
              Your Trust is
              <span className="block modern-heading">Our Foundation</span>
            </h2>
            <p className="text-xl theme-text-secondary max-w-3xl mx-auto">
              As a new agency, we're committed to building lasting relationships through exceptional service and unwavering integrity
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                icon: <Shield className="w-12 h-12 text-amber-500" />,
                title: "Complete Confidentiality",
                description: "Every case is handled with the utmost discretion and privacy. Your trust is our priority."
              },
              {
                icon: <CheckCircle className="w-12 h-12 text-amber-500" />,
                title: "Professional Standards",
                description: "We adhere to the highest ethical and professional standards in all our investigations."
              },
              {
                icon: <Award className="w-12 h-12 text-amber-500" />,
                title: "Modern Approach",
                description: "Combining traditional investigation methods with cutting-edge technology for optimal results."
              }
            ].map((commitment, index) => (
              <div
                key={index}
                className={`group modern-card p-8 transition-all duration-500 text-center ${
                  isVisible.commitment ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
                }`}
                style={{ transitionDelay: `${index * 150}ms` }}
              >
                <div className="flex justify-center mb-6">
                  {commitment.icon}
                </div>
                <h3 className="text-xl font-bold theme-text-primary mb-4">
                  {commitment.title}
                </h3>
                <p className="theme-text-secondary leading-relaxed">
                  {commitment.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section id="cta" className="py-24 theme-bg-accent relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 left-10 w-64 h-64 theme-bg-accent-primary rounded-full blur-3xl"></div>
          <div className="absolute bottom-10 right-10 w-80 h-80 theme-bg-accent-primary rounded-full blur-3xl"></div>
        </div>
        
        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div 
            className={`transition-all duration-1000 ${
              isVisible.cta ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
            }`}
          >
            <span className="inline-block px-4 py-2 theme-bg-accent-primary/20 theme-accent rounded-full text-sm font-medium mb-6">
              Ready to Get Started?
            </span>
            <h2 className="text-4xl md:text-6xl font-bold theme-text-primary mb-6 leading-tight">
              Get the Answers
              <span className="block modern-heading">You Deserve</span>
            </h2>
            <p className="text-xl md:text-2xl theme-text-secondary mb-10 leading-relaxed">
              Don't let uncertainty control your life. Take the first step towards clarity with a confidential consultation.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Link to="/contact" className="modern-btn text-lg px-10 py-4 text-white font-semibold flex items-center">
                Start Your Investigation
                <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </Link>
              <div className="flex items-center theme-text-secondary">
                <Lock className="w-5 h-5 mr-2" />
                <span>100% Confidential Consultation</span>
              </div>
            </div>

            <div className="mt-12 flex justify-center items-center space-x-8 theme-text-light">
              <div className="flex items-center">
                <CheckCircle className="w-5 h-5 theme-accent mr-2" />
                <span>Free Initial Consultation</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-5 h-5 theme-accent mr-2" />
                <span>24/7 Emergency Support</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Home

