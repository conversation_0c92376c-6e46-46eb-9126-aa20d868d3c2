import { useEffect } from 'react'
import { Shield, Target, Users, Award, CheckCircle } from 'lucide-react'

const About = () => {
  useEffect(() => {
    window.scrollTo(0, 0)
  }, [])
  const values = [
    {
      icon: <Shield className="w-8 h-8 text-amber-500" />,
      title: "Integrity",
      description: "We conduct all investigations with the highest ethical standards and complete honesty."
    },
    {
      icon: <Target className="w-8 h-8 text-amber-500" />,
      title: "Accuracy",
      description: "Our commitment to precision ensures reliable and actionable results for every case."
    },
    {
      icon: <Users className="w-8 h-8 text-amber-500" />,
      title: "Confidentiality",
      description: "Client privacy and discretion are paramount in all our investigative activities."
    },
    {
      icon: <Award className="w-8 h-8 text-amber-500" />,
      title: "Excellence",
      description: "We strive for excellence in every investigation, exceeding client expectations."
    }
  ]

  const capabilities = [
    "Licensed and certified investigators",
    "24/7 emergency response capability",
    "State-of-the-art investigative equipment",
    "Comprehensive case documentation",
    "Ethical investigation practices",
    "Modern technology integration"
  ]

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="py-20 theme-bg-accent-primary text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              About Emerald
            </h1>
            <p className="text-xl text-white opacity-90 max-w-3xl mx-auto">
              Your trusted partner in uncovering the truth with professionalism, integrity, and discretion
            </p>
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-20 theme-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold theme-text-primary mb-6">
                Our Story
              </h2>
              <div className="space-y-4 theme-text-secondary">
                <p>
                  Emerald was founded with a simple yet powerful mission: to provide
                  professional, ethical, and confidential investigation services to individuals
                  and businesses. Our journey began with a commitment to truth,
                  justice, and client satisfaction.
                </p>
                <p>
                  We are building a reputation as a trusted private investigation agency.
                  Our team combines traditional investigative techniques with modern technology
                  to deliver comprehensive solutions for complex cases.
                </p>
                <p>
                  We understand that every case is unique and requires a personalized approach. 
                  Whether it's a personal matter requiring discretion or a corporate investigation 
                  demanding thorough analysis, we bring the same level of professionalism and 
                  dedication to every assignment.
                </p>
              </div>
            </div>
            <div className="modern-card p-8">
              <h3 className="text-2xl font-semibold theme-text-primary mb-6">Our Mission</h3>
              <p className="theme-text-secondary mb-6">
                To provide reliable, ethical, and confidential investigation services that help
                our clients make informed decisions and find resolution to their concerns.
              </p>
              <h3 className="text-2xl font-semibold theme-text-primary mb-6">Our Vision</h3>
              <p className="theme-text-secondary">
                To be a leading private investigation agency, known for our integrity,
                professionalism, and commitment to uncovering the truth.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 theme-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold theme-text-primary mb-4">
              Our Core Values
            </h2>
            <p className="text-xl theme-text-secondary max-w-3xl mx-auto">
              The principles that guide every investigation and client interaction
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div key={index} className="modern-card p-6 text-center">
                <div className="flex justify-center mb-4">
                  {value.icon}
                </div>
                <h3 className="text-xl font-semibold theme-text-primary mb-3">
                  {value.title}
                </h3>
                <p className="theme-text-secondary">
                  {value.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 theme-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold theme-text-primary mb-4">
              Our Expert Team
            </h2>
            <p className="text-xl theme-text-secondary max-w-3xl mx-auto">
              Experienced professionals dedicated to delivering exceptional results
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-32 h-32 theme-bg-secondary rounded-full mx-auto mb-4 flex items-center justify-center">
                <Users className="w-16 h-16 theme-accent" />
              </div>
              <h3 className="text-xl font-semibold theme-text-primary mb-2">Professional Investigators</h3>
              <p className="theme-text-secondary">
                Trained professionals committed to delivering thorough and ethical investigation services
              </p>
            </div>
            <div className="text-center">
              <div className="w-32 h-32 theme-bg-secondary rounded-full mx-auto mb-4 flex items-center justify-center">
                <Target className="w-16 h-16 theme-accent" />
              </div>
              <h3 className="text-xl font-semibold theme-text-primary mb-2">Surveillance Specialists</h3>
              <p className="theme-text-secondary">
                Experts in covert operations and advanced surveillance techniques
              </p>
            </div>
            <div className="text-center">
              <div className="w-32 h-32 theme-bg-secondary rounded-full mx-auto mb-4 flex items-center justify-center">
                <Award className="w-16 h-16 theme-accent" />
              </div>
              <h3 className="text-xl font-semibold theme-text-primary mb-2">Digital Forensics Team</h3>
              <p className="theme-text-secondary">
                Specialists in cyber investigations and digital evidence analysis
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Capabilities Section */}
      <section className="py-20 theme-bg-accent-primary text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Our Capabilities
              </h2>
              <p className="text-white opacity-90 mb-8">
                We are equipped with modern tools and professional expertise to handle
                diverse investigation needs with integrity and precision.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-2">100%</div>
                  <div className="text-white opacity-80">Confidential</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-2">24/7</div>
                  <div className="text-white opacity-80">Available</div>
                </div>
              </div>
            </div>
            <div>
              <div className="space-y-4">
                {capabilities.map((capability, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="w-6 h-6 text-white flex-shrink-0" />
                    <span className="text-white opacity-90">{capability}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-20 theme-gradient text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Why Choose Emerald?
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
            <div>
              <h3 className="text-xl font-semibold mb-4">Professional Expertise</h3>
              <p className="text-amber-100">
                Deep understanding of investigation techniques and professional networks
              </p>
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-4">Advanced Technology</h3>
              <p className="text-amber-100">
                State-of-the-art equipment and modern investigative techniques
              </p>
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-4">Legal Compliance</h3>
              <p className="text-amber-100">
                All investigations conducted within legal boundaries and ethical guidelines
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default About

