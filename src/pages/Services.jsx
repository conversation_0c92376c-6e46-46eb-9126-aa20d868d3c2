import { Link } from 'react-router-dom'
import { ArrowRight, Shield, Users, Building, Eye, Search, FileText } from 'lucide-react'
import { Button } from '@/components/ui/button'

const Services = () => {
  const personalServices = [
    {
      icon: <Search className="w-8 h-8 text-amber-500" />,
      title: "Pre-Matrimonial Investigation",
      description: "Comprehensive background verification of prospective spouses including character, family, financial status, and personal history."
    },
    {
      icon: <Eye className="w-8 h-8 text-amber-500" />,
      title: "Post-Matrimonial Investigation",
      description: "Discreet investigation of marital infidelity, gathering evidence for divorce proceedings with photographic and video proof."
    },
    {
      icon: <Users className="w-8 h-8 text-amber-500" />,
      title: "Missing Person Investigation",
      description: "Professional tracing of missing individuals, runaway teenagers, lost relatives, and absconding persons."
    },
    {
      icon: <Shield className="w-8 h-8 text-amber-500" />,
      title: "Child Custody Cases",
      description: "Investigative support for child custody disputes, gathering evidence of parental fitness and living conditions."
    }
  ]

  const corporateServices = [
    {
      icon: <Building className="w-8 h-8 text-amber-500" />,
      title: "Employee Background Verification",
      description: "Thorough screening of potential employees including education, employment history, and criminal background checks."
    },
    {
      icon: <FileText className="w-8 h-8 text-amber-500" />,
      title: "Corporate Fraud Investigation",
      description: "Detection and investigation of financial fraud, embezzlement, and other corporate crimes with detailed reporting."
    },
    {
      icon: <Eye className="w-8 h-8 text-amber-500" />,
      title: "Business Intelligence",
      description: "Competitive intelligence gathering, due diligence for partnerships, and market research investigations."
    },
    {
      icon: <Shield className="w-8 h-8 text-amber-500" />,
      title: "Litigation Support",
      description: "Professional investigative support for legal proceedings, evidence gathering, and witness interviews."
    }
  ]

  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="py-20 theme-bg-accent-primary text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Our Investigation Services
            </h1>
            <p className="text-xl text-white opacity-90 max-w-3xl mx-auto">
              Comprehensive investigation solutions tailored to meet your personal and corporate needs
            </p>
          </div>
        </div>
      </section>

      {/* Personal Services Section */}
      <section className="py-20 theme-bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold theme-text-primary mb-4">
              Personal Investigation Services
            </h2>
            <p className="text-xl theme-text-secondary max-w-3xl mx-auto">
              Discreet and professional investigation services for personal matters
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            {personalServices.map((service, index) => (
              <div key={index} className="modern-card p-8">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    {service.icon}
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold theme-text-primary mb-3">
                      {service.title}
                    </h3>
                    <p className="theme-text-secondary">
                      {service.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center">
            <Button className="bg-amber-600 hover:bg-amber-700 text-white">
              <Link to="/services/personal" className="flex items-center">
                View All Personal Services
                <ArrowRight className="ml-2 w-4 h-4" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Corporate Services Section */}
      <section className="py-20 theme-bg-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold theme-text-primary mb-4">
              Corporate Investigation Services
            </h2>
            <p className="text-xl theme-text-secondary max-w-3xl mx-auto">
              Professional investigation solutions for businesses and organizations
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            {corporateServices.map((service, index) => (
              <div key={index} className="modern-card p-8">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    {service.icon}
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold theme-text-primary mb-3">
                      {service.title}
                    </h3>
                    <p className="theme-text-secondary">
                      {service.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center">
            <Button className="bg-amber-600 hover:bg-amber-700 text-white">
              <Link to="/services/corporate" className="flex items-center">
                View All Corporate Services
                <ArrowRight className="ml-2 w-4 h-4" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 theme-bg-accent-primary text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Our Investigation Process
            </h2>
            <p className="text-xl text-white opacity-90 max-w-3xl mx-auto">
              A systematic approach to ensure thorough and reliable results
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-white text-black rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold">1</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Consultation</h3>
              <p className="text-white opacity-80">
                Free confidential consultation to understand your requirements
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-white text-black rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold">2</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Planning</h3>
              <p className="text-white opacity-80">
                Develop a customized investigation strategy and timeline
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-white text-black rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold">3</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Investigation</h3>
              <p className="text-white opacity-80">
                Execute the investigation using professional techniques
              </p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-white text-black rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold">4</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Reporting</h3>
              <p className="text-white opacity-80">
                Deliver comprehensive report with evidence and findings
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-amber-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Need Professional Investigation Services?
          </h2>
          <p className="text-xl text-amber-100 mb-8 max-w-3xl mx-auto">
            Contact us today for a free, confidential consultation about your investigation needs
          </p>
          <Button size="lg" className="modern-btn-outline">
            <Link to="/contact" className="flex items-center">
              Get Free Consultation
              <ArrowRight className="ml-2 w-5 h-5" />
            </Link>
          </Button>
        </div>
      </section>
    </div>
  )
}

export default Services

