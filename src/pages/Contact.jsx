import { useState, useEffect } from 'react'
import { Phone, Mail, MapPin, Clock, Send, Shield, CheckCircle, AlertCircle, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { submitToGoogleForm } from '@/config/googleForm'

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    serviceType: '',
    message: ''
  })
  
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState(null)
  const [showNotification, setShowNotification] = useState(false)

  // Auto-dismiss success notification after 8 seconds
  useEffect(() => {
    if (submitStatus?.type === 'success') {
      setShowNotification(true)
      const timer = setTimeout(() => {
        setShowNotification(false)
        setTimeout(() => setSubmitStatus(null), 300) // Wait for fade out animation
      }, 8000)
      return () => clearTimeout(timer)
    } else if (submitStatus?.type === 'error') {
      setShowNotification(true)
    }
  }, [submitStatus])

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)
    setSubmitStatus(null)

    try {
      const result = await submitToGoogleForm(formData)
      
      if (result.success) {
        setSubmitStatus({ type: 'success', message: 'Thank you! Your inquiry has been submitted successfully. We will contact you within 24 hours.' })
        setFormData({
          name: '',
          email: '',
          phone: '',
          serviceType: '',
          message: ''
        })
      } else {
        setSubmitStatus({ type: 'error', message: 'There was an error submitting your form. Please try again or contact us directly.' })
      }
    } catch (error) {
      setSubmitStatus({ type: 'error', message: 'Network error. Please check your connection and try again.' })
    } finally {
      setIsSubmitting(false)
    }
  }

  const contactInfo = [
    {
      icon: <Phone className="w-5 h-5" />,
      title: "Phone",
      details: ["+91 90370 80050", "+91 90374 80050"],
      description: "24/7 Emergency Available"
    },
    {
      icon: <Mail className="w-5 h-5" />,
      title: "Email",
      details: ["<EMAIL>"],
      description: "Secure communication"
    },
    {
      icon: <MapPin className="w-5 h-5" />,
      title: "Location",
      details: ["Professional Services", "India"],
      description: "By appointment only"
    },
    {
      icon: <Clock className="w-5 h-5" />,
      title: "Hours",
      details: ["24/7 Emergency", "Office: 9 AM - 6 PM"],
      description: "Always available"
    }
  ]

  return (
    <div className="min-h-screen theme-bg-primary pt-16">
      {/* Hero Section */}
      <section className="py-16 theme-bg-secondary">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold theme-text-primary mb-4">
              Contact Us
            </h1>
            <p className="text-lg theme-text-secondary max-w-2xl mx-auto">
              Get in touch for a free, confidential consultation about your investigation needs
            </p>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            
            {/* Contact Form */}
            <div className="lg:col-span-2">
              <div className="modern-card p-8">
                <div className="flex items-center mb-6">
                  <Shield className="w-6 h-6 theme-accent mr-3" />
                  <h2 className="text-2xl font-bold theme-text-primary">Send us a message</h2>
                </div>

                {submitStatus && (
                  <div className={`mb-6 p-4 rounded-lg flex items-center gap-3 relative transform transition-all duration-500 ease-in-out modern-card ${
                    showNotification ? 'animate-in slide-in-from-top-2 fade-in scale-in-95' : 'animate-out slide-out-to-top-2 fade-out scale-out-95'
                  } ${
                    submitStatus.type === 'success'
                      ? 'border-l-4 border-l-green-500 bg-green-50 dark:bg-green-900/20 dark:border-l-green-400'
                      : 'border-l-4 border-l-red-500 bg-red-50 dark:bg-red-900/20 dark:border-l-red-400'
                  }`}>
                    <div className={`${submitStatus.type === 'success' ? 'animate-bounce' : 'animate-pulse'}`}>
                      {submitStatus.type === 'success' ? (
                        <CheckCircle className={`w-5 h-5 flex-shrink-0 ${
                          submitStatus.type === 'success' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                        }`} />
                      ) : (
                        <AlertCircle className={`w-5 h-5 flex-shrink-0 ${
                          submitStatus.type === 'success' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                        }`} />
                      )}
                    </div>
                    <div className="flex-1">
                      <p className={`text-sm font-medium ${
                        submitStatus.type === 'success'
                          ? 'text-green-800 dark:text-green-200'
                          : 'text-red-800 dark:text-red-200'
                      }`}>
                        {submitStatus.message}
                      </p>
                      {submitStatus.type === 'success' && (
                        <div className="mt-2">
                          <div className="w-full bg-green-200 dark:bg-green-700 rounded-full h-1.5">
                            <div className="bg-green-600 dark:bg-green-400 h-1.5 rounded-full animate-pulse" style={{width: '100%'}}></div>
                          </div>
                          <p className="text-xs text-green-600 dark:text-green-400 mt-1 animate-pulse">We will contact you soon...</p>
                        </div>
                      )}
                    </div>
                    <button
                      onClick={() => {
                        setShowNotification(false)
                        setTimeout(() => setSubmitStatus(null), 300)
                      }}
                      className="ml-2 theme-text-light hover:theme-text-primary transition-colors"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                )}
                
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium theme-text-primary mb-2">
                        Full Name *
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        required
                        value={formData.name}
                        onChange={handleChange}
                        className="w-full px-4 py-3 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-white transition-all duration-200 shadow-sm"
                        placeholder="Your full name"
                      />
                    </div>
                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium theme-text-primary mb-2">
                        Phone Number *
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        required
                        value={formData.phone}
                        onChange={handleChange}
                        className="w-full px-4 py-3 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-white transition-all duration-200 shadow-sm"
                        placeholder="+91 90370 80050"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium theme-text-primary mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      required
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full px-4 py-3 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-white transition-all duration-200 shadow-sm"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label htmlFor="serviceType" className="block text-sm font-medium theme-text-primary mb-2">
                      Service Type *
                    </label>
                    <select
                      id="serviceType"
                      name="serviceType"
                      required
                      value={formData.serviceType}
                      onChange={handleChange}
                      className="w-full px-4 py-3 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-white transition-all duration-200 shadow-sm"
                    >
                      <option value="">Select a service</option>
                      <option value="pre-matrimonial">Pre-Matrimonial Investigation</option>
                      <option value="post-matrimonial">Post-Matrimonial Investigation</option>
                      <option value="missing-person">Missing Person Investigation</option>
                      <option value="corporate">Corporate Investigation</option>
                      <option value="background-check">Background Verification</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium theme-text-primary mb-2">
                      Message *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      required
                      rows={4}
                      value={formData.message}
                      onChange={handleChange}
                      className="w-full px-4 py-3 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-white transition-all duration-200 shadow-sm resize-none"
                      placeholder="Please provide details about your investigation needs..."
                    ></textarea>
                  </div>

                  <div className="p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
                    <div className="flex items-center gap-2 text-amber-800 dark:text-amber-200">
                      <Shield className="w-4 h-4" />
                      <span className="text-sm font-medium">Confidentiality Guarantee</span>
                    </div>
                    <p className="text-sm text-amber-700 dark:text-amber-300 mt-1">
                      All information shared will be kept strictly confidential and secure.
                    </p>
                  </div>

                  <Button 
                    type="submit" 
                    disabled={isSubmitting}
                    className="w-full modern-btn py-3 text-white font-medium"
                  >
                    {isSubmitting ? (
                      <div className="flex items-center justify-center gap-2">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        Sending...
                      </div>
                    ) : (
                      <>
                        <Send className="w-4 h-4 mr-2" />
                        Send Message
                      </>
                    )}
                  </Button>
                </form>
              </div>
            </div>

            {/* Contact Information */}
            <div className="space-y-6">
              <div className="modern-card p-6">
                <h3 className="text-xl font-bold theme-text-primary mb-6">Get in Touch</h3>
                
                <div className="space-y-6">
                  {contactInfo.map((info, index) => (
                    <div key={index} className="flex items-start gap-4">
                      <div className="flex-shrink-0 w-10 h-10 theme-bg-accent-primary rounded-lg flex items-center justify-center text-white">
                        {info.icon}
                      </div>
                      <div>
                        <h4 className="font-semibold theme-text-primary mb-1">
                          {info.title}
                        </h4>
                        {info.details.map((detail, detailIndex) => (
                          <p key={detailIndex} className="theme-text-secondary text-sm font-medium">
                            {detail}
                          </p>
                        ))}
                        <p className="text-xs theme-text-light mt-1">
                          {info.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Emergency Contact */}
              <div className="modern-card p-6 border-l-4 border-red-500">
                <h4 className="font-semibold text-red-600 mb-2">Emergency Services</h4>
                <p className="text-sm theme-text-secondary mb-3">
                  For urgent matters requiring immediate attention
                </p>
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4 text-red-600" />
                  <span className="font-semibold text-red-600">+91 90374 83600</span>
                </div>
                <p className="text-xs theme-text-light mt-1">
                  Available 24/7
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Contact

