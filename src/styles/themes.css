/* Modern Theme System */

:root {
  /* Light Theme (Default) */
  --primary-bg: #ffffff;
  --secondary-bg: #f8fafc;
  --accent-bg: #f1f5f9;
  --card-bg: #ffffff;
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-light: #64748b;
  --accent-primary: #3b82f6;
  --accent-secondary: #60a5fa;
  --accent-hover: #2563eb;
  --border-color: #e2e8f0;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --gradient-start: #3b82f6;
  --gradient-end: #60a5fa;
}

/* Dark Theme */
.dark,
[data-theme="dark"] {
  --primary-bg: #0f172a;
  --secondary-bg: #1e293b;
  --accent-bg: #334155;
  --card-bg: #1e293b;
  --text-primary: #ffffff;
  --text-secondary: #cbd5e1;
  --text-light: #94a3b8;
  --accent-primary: #60a5fa;
  --accent-secondary: #93c5fd;
  --accent-hover: #3b82f6;
  --border-color: #475569;
  --shadow-color: rgba(0, 0, 0, 0.3);
  --gradient-start: #60a5fa;
  --gradient-end: #93c5fd;
}

/* Black and White Theme */
.blackwhite,
[data-theme="blackwhite"] {
  --primary-bg: #ffffff;
  --secondary-bg: #f9fafb;
  --accent-bg: #f3f4f6;
  --card-bg: #ffffff;
  --text-primary: #000000;
  --text-secondary: #374151;
  --text-light: #6b7280;
  --accent-primary: #000000;
  --accent-secondary: #1f2937;
  --accent-hover: #374151;
  --border-color: #d1d5db;
  --shadow-color: rgba(0, 0, 0, 0.15);
  --gradient-start: #000000;
  --gradient-end: #374151;
}

/* Light Theme (explicit) */
.light,
[data-theme="light"] {
  --primary-bg: #ffffff;
  --secondary-bg: #f8fafc;
  --accent-bg: #f1f5f9;
  --card-bg: #ffffff;
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-light: #64748b;
  --accent-primary: #3b82f6;
  --accent-secondary: #60a5fa;
  --accent-hover: #2563eb;
  --border-color: #e2e8f0;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --gradient-start: #3b82f6;
  --gradient-end: #60a5fa;
}

/* Theme Application Classes */
.theme-bg-primary {
  background-color: var(--primary-bg);
}

.theme-bg-secondary {
  background-color: var(--secondary-bg);
}

.theme-bg-accent {
  background-color: var(--accent-bg);
}

.theme-bg-card {
  background-color: var(--card-bg);
}

.theme-text-primary {
  color: var(--text-primary);
}

.theme-text-secondary {
  color: var(--text-secondary);
}

.theme-text-light {
  color: var(--text-light);
}

.theme-accent {
  color: var(--accent-primary);
}

.theme-bg-accent-primary {
  background-color: var(--accent-primary);
}

.theme-bg-accent-hover:hover {
  background-color: var(--accent-hover);
}

.theme-border {
  border-color: var(--border-color);
}

.theme-gradient {
  background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
}

/* Modern Card Styles */
.modern-card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 1.5rem;
  box-shadow: 0 4px 6px -1px var(--shadow-color), 0 2px 4px -1px var(--shadow-color);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.modern-card:hover {
  box-shadow: 0 20px 25px -5px var(--shadow-color), 0 10px 10px -5px var(--shadow-color);
  transform: translateY(-2px);
}



/* Glass effect for glass theme */
.glass .modern-card,
[data-theme="glass"] .modern-card {
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Modern Button Styles */
.modern-btn {
  background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
  border: none;
  border-radius: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 6px -1px var(--shadow-color);
  position: relative;
  overflow: hidden;
}

.modern-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.modern-btn:hover::before {
  left: 100%;
}

.modern-btn:hover {
  background: linear-gradient(135deg, var(--accent-hover), var(--accent-primary));
  box-shadow: 0 10px 15px -3px var(--shadow-color);
  transform: translateY(-2px);
}

.modern-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.modern-btn:disabled::before {
  display: none;
}

/* Outline button variant */
.modern-btn-outline {
  background: transparent;
  border: 2px solid var(--accent-primary);
  color: var(--accent-primary);
  border-radius: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
}

.modern-btn-outline:hover {
  background: var(--accent-primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px var(--shadow-color);
}

/* Input Styles */
input, select, textarea {
  transition: all 0.2s ease;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

/* Smooth Transitions */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-card {
    border-radius: 0.75rem;
    margin: 0 -0.5rem;
  }
}

/* Animation Keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Removed problematic animations that were causing responsiveness issues */

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.5s ease-out;
}

/* Theme Toggle Styles */
.theme-toggle {
  position: fixed;
  top: 5rem;
  right: 1rem;
  z-index: 50;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  padding: 0.75rem;
  box-shadow: 0 4px 6px -1px var(--shadow-color);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.theme-toggle:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px var(--shadow-color);
}

/* Modern heading style */
.modern-heading {
  background: var(--gradient-start);
  background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced focus styles */
input:focus, select:focus, textarea:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--accent-primary), 0.1);
  border-color: var(--accent-primary);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--secondary-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-hover);
}

