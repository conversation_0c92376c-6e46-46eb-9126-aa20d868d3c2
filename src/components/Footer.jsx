import { Link } from 'react-router-dom'
import { Phone, Mail, MapPin, Clock } from 'lucide-react'

const Footer = () => {
  return (
    <footer className="theme-bg-accent-primary text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1 lg:col-span-2">
            <div className="mb-4">
              <div className="text-2xl font-bold mb-2">Emerald</div>
              <div className="text-sm text-white opacity-80">Private Investigation Services</div>
            </div>
            <p className="text-white opacity-90 mb-4 max-w-md">
              Your trusted partner for discreet and professional investigation services.
              We provide confidential, ethical, and effective solutions for personal and corporate needs.
            </p>
            <div className="flex items-center space-x-2 text-white opacity-90">
              <Clock className="w-4 h-4" />
              <span className="text-sm">24/7 Emergency Services Available</span>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li><Link to="/" className="text-white opacity-80 hover:opacity-100 transition-opacity">Home</Link></li>
              <li><Link to="/about" className="text-white opacity-80 hover:opacity-100 transition-opacity">About Us</Link></li>
              <li><Link to="/services" className="text-white opacity-80 hover:opacity-100 transition-opacity">Services</Link></li>
              <li><Link to="/faq" className="text-white opacity-80 hover:opacity-100 transition-opacity">FAQ</Link></li>
              <li><Link to="/contact" className="text-white opacity-80 hover:opacity-100 transition-opacity">Contact</Link></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact Info</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Phone className="w-4 h-4 text-white opacity-90" />
                <span className="text-white opacity-80">+91 90374 83600</span>
              </div>
              <div className="flex items-center space-x-2">
                <Mail className="w-4 h-4 text-white opacity-90" />
                <span className="text-white opacity-80"><EMAIL></span>
              </div>
              <div className="flex items-start space-x-2">
                <MapPin className="w-4 h-4 text-white opacity-90 mt-1" />
                <a
                  href="https://maps.app.goo.gl/Yrb5JSsyaDfZqwiz7"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-white opacity-80 hover:opacity-100 transition-opacity"
                >
                  Door no:295<br />
                  Ground Floor<br />
                  Prashanti Shopping mall, Haripad<br />
                  Near IOB Bank
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-white border-opacity-20 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-white opacity-70 text-sm mb-4 md:mb-0">
              © 2025 Emerald. All rights reserved.
            </div>
            <div className="flex space-x-6 text-sm">
              <Link to="/privacy" className="text-white opacity-70 hover:opacity-100 transition-opacity">
                Privacy Policy
              </Link>
              <Link to="/terms" className="text-white opacity-70 hover:opacity-100 transition-opacity">
                Terms of Service
              </Link>
              <Link to="/disclaimer" className="text-white opacity-70 hover:opacity-100 transition-opacity">
                Disclaimer
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer

