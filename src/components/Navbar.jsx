import { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { Menu, X, Shield, Phone, Mail } from 'lucide-react'
import { Button } from '@/components/ui/button'

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false)
  const location = useLocation()

  const navigation = [
    { name: 'Home', href: '/' },
    { name: 'About', href: '/about' },
    { 
      name: 'Solutions', 
      href: '/services',
      submenu: [
        { name: 'Personal Investigations', href: '/services/personal' },
        { name: 'Corporate Investigations', href: '/services/corporate' }
      ]
    },
    { name: 'FAQ', href: '/faq' },
    { name: 'Contact', href: '/contact' }
  ]

  const isActive = (path) => location.pathname === path

  return (
    <>
      {/* Top Bar - Hidden on mobile */}
      <div className="hidden md:block theme-bg-accent-primary text-white py-2 px-4">
        <div className="max-w-7xl mx-auto flex justify-between items-center text-sm">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Phone className="w-4 h-4" />
              <span>+91 90374 83600</span>
            </div>
            <div className="flex items-center space-x-2">
              <Mail className="w-4 h-4" />
              <span><EMAIL></span>
            </div>
          </div>
          <div className="text-white font-medium">
            24/7 Confidential Services
          </div>
        </div>
      </div>

      {/* Main Navigation */}
      <nav className="theme-bg-primary shadow-lg sticky top-0 z-50 theme-border border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-3">
              <img
                src="/Emarald lOGO-1.png"
                alt="Emerald Logo"
                className="w-10 h-10 hover:scale-105 transition-transform duration-200"
              />
              <div>
                <div className="text-xl font-bold theme-text-primary">Emerald</div>
                <div className="text-xs theme-text-secondary">Truth & Clarity Solutions</div>
              </div>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              {navigation.map((item) => (
                <div key={item.name} className="relative group">
                  <Link
                    to={item.href}
                    className={`px-4 py-2 text-sm font-medium transition-all duration-200 rounded-lg ${
                      isActive(item.href)
                        ? 'theme-accent bg-white bg-opacity-10'
                        : 'theme-text-primary hover:theme-accent hover:bg-white hover:bg-opacity-10'
                    } ${item.submenu ? 'flex items-center space-x-1' : ''}`}
                  >
                    <span>{item.name}</span>
                    {item.submenu && (
                      <svg className="w-4 h-4 transition-transform duration-200 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    )}
                  </Link>
                  
                  {/* Dropdown Menu */}
                  {item.submenu && (
                    <div className="absolute left-0 mt-2 w-64 modern-card opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50 shadow-lg">
                      <div className="py-2">
                        {item.submenu.map((subItem) => (
                          <Link
                            key={subItem.name}
                            to={subItem.href}
                            className="block px-4 py-3 text-sm theme-text-primary hover:theme-bg-secondary hover:theme-accent transition-colors duration-200 border-l-4 border-transparent hover:border-current"
                          >
                            {subItem.name}
                          </Link>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
              
              <Button className="modern-btn text-white">
                <Link to="/consultation">Get Consultation</Link>
              </Button>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <button
                onClick={() => setIsOpen(!isOpen)}
                className="theme-text-primary hover:theme-accent"
              >
                {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden theme-bg-primary theme-border border-t">
            <div className="px-2 pt-2 pb-3 space-y-1">
              {navigation.map((item) => (
                <div key={item.name}>
                  <Link
                    to={item.href}
                    className={`block px-3 py-2 text-base font-medium ${
                      isActive(item.href)
                        ? 'theme-accent theme-bg-secondary'
                        : 'theme-text-primary hover:theme-accent hover:theme-bg-secondary'
                    }`}
                    onClick={() => setIsOpen(false)}
                  >
                    {item.name}
                  </Link>
                  {item.submenu && (
                    <div className="pl-4">
                      {item.submenu.map((subItem) => (
                        <Link
                          key={subItem.name}
                          to={subItem.href}
                          className="block px-3 py-2 text-sm theme-text-secondary hover:theme-accent"
                          onClick={() => setIsOpen(false)}
                        >
                          {subItem.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ))}
              <div className="px-3 py-2">
                <Button className="w-full modern-btn text-white">
                  <Link to="/consultation">Get Consultation</Link>
                </Button>
              </div>
            </div>
          </div>
        )}
      </nav>
    </>
  )
}

export default Navbar

