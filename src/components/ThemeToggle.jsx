import { useState, useEffect } from 'react'
import { <PERSON>, Moon, Palette } from 'lucide-react'

const ThemeToggle = () => {
  const [currentTheme, setCurrentTheme] = useState('light')

  const themes = [
    {
      id: 'light',
      name: 'Light Mode',
      icon: <Sun className="w-5 h-5" />
    },
    {
      id: 'dark',
      name: 'Dark Mode',
      icon: <Moon className="w-5 h-5" />
    },
    {
      id: 'blackwhite',
      name: 'High Contrast',
      icon: <Palette className="w-5 h-5" />
    }
  ]

  useEffect(() => {
    const savedTheme = localStorage.getItem('emerald-theme') || 'light'
    setCurrentTheme(savedTheme)
    applyTheme(savedTheme)
  }, [])

  const applyTheme = (themeId) => {
    // Remove all theme classes
    document.documentElement.classList.remove('light', 'dark', 'blackwhite', 'glass')
    // Add the new theme class
    document.documentElement.classList.add(themeId)
    // Also set data attribute for CSS selectors
    document.documentElement.setAttribute('data-theme', themeId)
  }

  const changeTheme = () => {
    const currentIndex = themes.findIndex(t => t.id === currentTheme)
    const nextIndex = (currentIndex + 1) % themes.length
    const nextTheme = themes[nextIndex].id

    setCurrentTheme(nextTheme)
    applyTheme(nextTheme)
    localStorage.setItem('emerald-theme', nextTheme)
  }

  const getCurrentTheme = () => {
    return themes.find(t => t.id === currentTheme) || themes[0]
  }

  return (
    <div className="fixed top-20 right-4 z-50">
      <button
        onClick={changeTheme}
        className="modern-card p-3 hover:scale-105 transition-all duration-200 group"
        title={`Switch to ${themes[(themes.findIndex(t => t.id === currentTheme) + 1) % themes.length].name}`}
      >
        <div className="flex items-center space-x-2">
          {getCurrentTheme().icon}
          <span className="text-sm font-medium theme-text-primary hidden sm:block">
            {getCurrentTheme().name}
          </span>
        </div>
      </button>
    </div>
  )
}

export default ThemeToggle

