export const GOOGLE_FORM_CONFIG = {
  url: import.meta.env.VITE_GOOGLE_FORM_URL,
  fields: {
    name: 'entry.1981466084',     // Full Name field
    email: 'entry.842190773',    // Email field
    phone: 'entry.389933790',    // Phone Number field
    serviceType: 'entry.447929630', // Service Type field
    message: 'entry.1983763282'  // Message field
  }
}

export const submitToGoogleForm = async (formData) => {
  const formBody = new FormData()

  // Map form data to Google Form fields
  formBody.append(GOOGLE_FORM_CONFIG.fields.name, formData.name)
  formBody.append(GOOGLE_FORM_CONFIG.fields.email, formData.email)
  formBody.append(GOOGLE_FORM_CONFIG.fields.phone, formData.phone)
  formBody.append(GOOGLE_FORM_CONFIG.fields.serviceType, formData.serviceType)
  formBody.append(GOOGLE_FORM_CONFIG.fields.message, formData.message)

  try {
    await fetch(GOOGLE_FORM_CONFIG.url, {
      method: 'POST',
      body: formBody,
      mode: 'no-cors'
    })
    return { success: true }
  } catch (error) {
    console.error('Form submission error:', error)
    return { success: false, error }
  }
}