<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="60" cy="60" r="58" fill="url(#gradient)" stroke="url(#borderGradient)" stroke-width="4"/>
  
  <!-- Shield Shape -->
  <path d="M60 20L45 30V50C45 65 52.5 78 60 85C67.5 78 75 65 75 50V30L60 20Z" fill="white" fill-opacity="0.9"/>
  
  <!-- Magnifying Glass -->
  <circle cx="55" cy="45" r="8" stroke="currentColor" stroke-width="2.5" fill="none"/>
  <path d="M61 51L67 57" stroke="currentColor" stroke-width="2.5" stroke-linecap="round"/>
  
  <!-- Eye Symbol (Investigation) -->
  <ellipse cx="60" cy="65" rx="12" ry="8" stroke="currentColor" stroke-width="2" fill="none"/>
  <circle cx="60" cy="65" r="3" fill="currentColor"/>
  
  <!-- Gradient Definitions -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="borderGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#60a5fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Letter E for Emerald -->
  <text x="60" y="95" font-family="Inter, sans-serif" font-size="16" font-weight="700" text-anchor="middle" fill="white">E</text>
</svg>
